import _ from "lodash";
export const pdfType = "application/pdf";

export const removeExtraSpaces = (text: string): string => {
  const spaceRegex = /\s+/g;
  const trimmedText = text?.replace(spaceRegex, " ")?.trim();
  return trimmedText;
};

export const breakLongWord = (word: string, maxLength: number) => {
  const segments = [];
  while (word?.length > 0) {
    segments.push(word?.slice(0, maxLength));
    word = word?.slice(maxLength);
  }
  return segments;
};

export const findDate = (
  dates: string[],
  isEarliest: boolean,
  isLatest: boolean
): string => {
  let formattedDate = "";
  const filteredDates = dates?.filter(Boolean);
  if (filteredDates && filteredDates.length > 0) {
    const dateObjects = filteredDates.map((dateString) => {
      const [monthStr, dayStr, yearStr] = dateString?.split("-");
      const month = parseInt(monthStr, 10);
      const day = parseInt(dayStr, 10);
      const year = parseInt(yearStr, 10);
      return new Date(year, month - 1, day);
    });

    if (isEarliest) {
      let earliestDate = dateObjects?.[0];
      for (let i = 1; i < dateObjects?.length; i++) {
        if (dateObjects?.[i] < earliestDate) {
          earliestDate = dateObjects?.[i];
        }
      }
      const earliestMonth = `0${earliestDate?.getMonth() + 1}`?.slice(-2);
      const earliestDay = `0${earliestDate?.getDate()}`?.slice(-2);
      const earliestYear = earliestDate?.getFullYear();
      formattedDate = `${earliestMonth}-${earliestDay}-${earliestYear}`;
    } else if (isLatest) {
      let latestDate = dateObjects?.[0];
      for (let i = 1; i < dateObjects?.length; i++) {
        if (dateObjects?.[i] > latestDate) {
          latestDate = dateObjects?.[i];
        }
      }
      const latestMonth = `0${latestDate?.getMonth() + 1}`?.slice(-2);
      const latestDay = `0${latestDate?.getDate()}`?.slice(-2);
      const latestYear = latestDate?.getFullYear();
      formattedDate = `${latestMonth}-${latestDay}-${latestYear}`;
    }
  } else {
    formattedDate = "";
  }

  return formattedDate;
};

const fileTypes = (): string[] => {
  let allowedFileFormats = [];
  allowedFileFormats = ["application/pdf"];
  return allowedFileFormats;
};

export const isFileTypeAllowed = (fileType: string): boolean => {
  const formats = fileTypes();
  return formats.includes(fileType);
};

export const isFileTypeNotAllowed = (fileType: string): boolean => {
  const formats = fileTypes();
  return !formats.includes(fileType);
};

export const getFileUploadErrors = (
  filesArray: File[]
): { [key: string]: boolean } => {
  const hasInvalidFileTypes = filesArray.some((file: File) =>
    isFileTypeNotAllowed(file.type)
  );
  return {
    hasInvalidFileTypes,
  };
};

export const mostFrequentPatientDetail = (names: any) => {
  if (names?.length === 0) return null;

  const nameFrequency = names?.reduce((acc: any, name: any) => {
    acc[name] = (acc[name] || 0) + 1;
    return acc;
  }, {});

  let mostFrequentName = names?.[0];
  let maxFrequency = 1;

  for (const name in nameFrequency) {
    if (nameFrequency[name] > maxFrequency) {
      mostFrequentName = name;
      maxFrequency = nameFrequency[name];
    }
  }
  return mostFrequentName;
};

export async function fetchCsrfToken() {
  const response = await fetch("/api/get-csrf-token", {
    credentials: "include",
  });
  const data = await response.json();
  return data.csrfToken || "";
}

export const calculatePatientBMI = (weight: number, height: number) => {
  let bmiValue;
  if (weight > 0 && height > 0) {
    bmiValue = ((weight / height ** 2) * 703).toFixed(2);
  }
  return bmiValue;
};

export const calculatePatientHWByDate = (values: any) => {
  let value;
  if (values?.length > 1) {
    const filteredDates = _.filter(values, (obj) => !!obj?.date);
    if (filteredDates?.length > 1) {
      value = filteredDates?.find((p: any) => {
        return (
          p.date ===
          findDate(
            values?.map((x: any) => x?.date),
            false,
            true
          )
        );
      })?.value;
    } else {
      if (_.some(values, (obj) => !!obj?.date)) {
        value = Number(_.find(values, (obj) => !!obj?.date)?.value)?.toFixed(2);
      } else {
        value = Number(values?.[0]?.value)?.toFixed(2);
      }
    }
  } else {
    value = Number(values?.[0]?.value)?.toFixed(2);
  }
  return value;
};
