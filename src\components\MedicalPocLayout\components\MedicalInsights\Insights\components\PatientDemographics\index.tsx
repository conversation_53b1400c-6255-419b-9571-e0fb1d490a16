import React from "react";
import { PatientDemographicsProps } from "./typings";
import { motion } from "framer-motion";
import { usePatientDetails } from "@hooks/usePatientDetails";

const PatientDemographics: React.FC<PatientDemographicsProps> = ({
  patientDemographics,
}) => {
  const {
    patientName,
    patientDOB,
    patientGender,
    patientAge,
    patientBMI,
    patientHeight,
    patientWeight,
    noDemographics,
  } = usePatientDetails(patientDemographics);

  return (
    <div className="overflow-hidden rounded-b-xl">
      {noDemographics ? (
        <motion.div
          className="text-center text-brand-secondary rounded-lg border m-4 font-semibold px-12 py-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          No patient demographics available.
        </motion.div>
      ) : (
        <motion.div
          className="flex flex-col font-medium"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{
            duration: 0.3,
          }}
        >
          {!!patientName && (
            <div className="flex">
              <span className="flex-[0.3] border-y-2 border-r-2 px-12 py-3 font-semibold">
                Name
              </span>
              <span className="flex-1 px-12 py-3 border-y-2">
                {patientName}
              </span>
            </div>
          )}
          {!!patientDOB && (
            <div className="flex">
              <span className="flex-[0.3] border-b-2 border-r-2 px-12 py-3 font-semibold">
                Date Of Birth
              </span>
              <span className="flex-1 px-12 py-3 border-b-2">{patientDOB}</span>
            </div>
          )}
          {!!patientGender && (
            <div className="flex">
              <span className="flex-[0.3] border-b-2 border-r-2 px-12 py-3 font-semibold">
                Gender
              </span>
              <span className="flex-1 px-12 py-3 border-b-2">
                {patientGender}
              </span>
            </div>
          )}
          {!!patientAge && (
            <div className="flex">
              <span className="flex-[0.3] border-b-2 border-r-2 px-12 py-3 font-semibold">
                Age
              </span>
              <span className="flex-1 px-12 py-3 border-b-2">{patientAge}</span>
            </div>
          )}
          {!!patientHeight && (
            <div className="flex">
              <span className="flex-[0.3] border-b-2 border-r-2 px-12 py-3 font-semibold">
                Height <sup>(inch)</sup>
              </span>
              <span className="flex-1 px-12 py-3 border-b-2">
                {patientHeight}
              </span>
            </div>
          )}
          {!!patientWeight && (
            <div className="flex">
              <span className="flex-[0.3] border-b-2 border-r-2 px-12 py-3 font-semibold">
                Weight <sup>(lbs)</sup>
              </span>
              <span className="flex-1 px-12 py-3 border-b-2">
                {patientWeight}
              </span>
            </div>
          )}
          {!!patientBMI && (
            <div className="flex">
              <span className="flex-[0.3] border-r-2 px-12 py-3 font-semibold">
                BMI
              </span>
              <span className="flex-1 px-12 py-3">{patientBMI}</span>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

export default PatientDemographics;
