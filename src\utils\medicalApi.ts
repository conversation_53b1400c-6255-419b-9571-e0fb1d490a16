export const fetchJsonFromS3API = async (bucket: string, key: string) => {
  const response = await fetch(`/api/read-json?bucket=${bucket}&key=${key}`);
  const result = await response.json();
  if (result.success) {
    return result.data;
  } else {
    throw new Error(result.error || "Failed to fetch S3 data");
  }
};

export const fetchProjectResponseFromS3API = async (
  bucket: string,
  userId: string,
  projectId: string | any
) => {
  const response = await fetch(
    `/api/read-multiple-json?bucket=${bucket}&userId=${userId}&projectId=${projectId}`
  );
  const result = await response.json();
  if (result.success) {
    return result.data;
  } else {
    throw new Error(result.error || "Failed to fetch S3 data");
  }
};

export const existingProjectPages = async (projectId: string | any) => {
  const response = await fetch(
    `/api/projects/existing-project-pages?projectId=${projectId}`
  );
  const result = await response.json();
  if (result.success) {
    return result.pages;
  } else {
    throw new Error(result.error || "Failed to get project pages");
  }
};

export const fetchPDF = async (
  projectId: string | any,
  userId: string,
  documentName: string
): Promise<{ pdfData: string | null }> => {
  const pdfInfo: { pdfData: string | null } = {
    pdfData: null,
  };
  try {
    const response = await fetch(
      `/api/projects/${projectId}/pdf?userId=${userId}&documentName=${documentName}`
    );
    if (response.ok) {
      const pdfData = await response.arrayBuffer();
      const uint8Data = new Uint8Array(pdfData);
      const blob = new Blob([uint8Data], { type: "application/pdf" });
      const url = URL.createObjectURL(blob);
      pdfInfo.pdfData = url;
    }
  } catch (error) {
    console.error("Failed to fetch PDF", error);
  }
  return pdfInfo;
};

export const fetchPDFForSample = async (
  sampleNo: number,
  documentName: string
): Promise<{ pdfData: string | null }> => {
  const pdfInfo: { pdfData: string | null } = {
    pdfData: null,
  };
  try {
    const response = await fetch(
      `/api/samples/pdf?sampleNo=${sampleNo}&documentName=${documentName}`
    );
    if (response.ok) {
      const pdfData = await response.arrayBuffer();
      const uint8Data = new Uint8Array(pdfData);
      const blob = new Blob([uint8Data], { type: "application/pdf" });
      const url = URL.createObjectURL(blob);
      pdfInfo.pdfData = url;
    }
  } catch (error) {
    console.error("Failed to fetch PDF", error);
  }
  return pdfInfo;
};
