require('dotenv').config();
const cron = require("node-cron");
const fetch = require('node-fetch');

const { SQSClient, ReceiveMessageCommand, DeleteMessageCommand } = require("@aws-sdk/client-sqs");

const sqsClient = new SQSClient({ region: process.env.NEXT_PUBLIC_AWS_REGION });

const docStatus = {
  completed: "Completed",
  failed: "Failed",
};

const checkDocumentCompleteStatus = async (projectId) => {
  let finalResponse;
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_DOMAIN}/api/projects/documents/check-document-complete-status?projectId=${projectId}`
  );
  if(response.ok){
    finalResponse = await response.json();
  }
  return finalResponse;
};

 const updateDocumentStatus = async (
  projectId,
  documentName,
  status
) => {
  const url = `${process.env.NEXT_PUBLIC_API_DOMAIN}/api/projects/documents/update-document-status`;
  return await fetch(url, {
    method: "PATCH",
    body: JSON.stringify({ projectId, documentName, status }),
  });
};

const updateProjectStatus = async (
  projectId,
  status
) => {
  return await fetch(`${process.env.NEXT_PUBLIC_API_DOMAIN}/api/projects/update-project-status`, {
    method: "PATCH",
    body: JSON.stringify({ projectId, status }),
  });
};

const processMessagesFromQueue = async () => {
  let allMessagesProcessed = false;

  while (!allMessagesProcessed) {
    const receiveParams = {
      QueueUrl: process.env.NEXT_PUBLIC_SQS_CONSUME_QUEUE_URL,
      MessageAttributeNames: ["All"],
      MaxNumberOfMessages: 10,
      WaitTimeSeconds: 20,
    };

    try {
      const receiveCommand = new ReceiveMessageCommand(receiveParams);
      const data = await sqsClient.send(receiveCommand);
      
      if (data.Messages && data.Messages.length > 0) {
        console.log(`Reading ${data.Messages.length} messages...`);
    
        for (const message of data.Messages) {
          const body = JSON.parse(message.Body);
          
          console.log(`Message Body: ${message.Body}} `);

          const { project_id, document_name, status } = body;
         
          await updateDocumentStatus(
            project_id,
            document_name,
            docStatus[status]
          );
          
          console.log("Document status updated successfully");

          const { allCompleted } = await checkDocumentCompleteStatus(
            project_id
          );

          console.log("Are all documents processing completed? ", allCompleted);

          if (allCompleted) {
            await updateProjectStatus(project_id, "Completed");
            console.log('Project status updated successfully');
          }

          const deleteParams = {
            QueueUrl: process.env.NEXT_PUBLIC_SQS_CONSUME_QUEUE_URL,
            ReceiptHandle: message.ReceiptHandle,
          };
          const deleteCommand = new DeleteMessageCommand(deleteParams);
          await sqsClient.send(deleteCommand);
          console.log("Message deleted from queue successfully");
        }
      } else {
        allMessagesProcessed = true;
      }
    } catch (error) {
      console.error("Error processing SQS message:", error.message);
    }
  }
  console.log("All messages processed successfully.");
};

cron.schedule("*/5 * * * * *", async () => {
  console.log("Running Cron Job: Checking for SQS Messages...");
    await processMessagesFromQueue();
});
